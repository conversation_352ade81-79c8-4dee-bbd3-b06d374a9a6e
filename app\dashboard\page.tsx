import { redirect } from "next/navigation";
import { createClient, getCurrentUser } from "@/lib/supabase/server";
import { sampleAssets } from "@/lib/sample-data";
import { DashboardContent } from "./dashboard-content";

export default async function DashboardPage() {
  const supabase = await createClient();

  // Check authentication
  const { data: { user: authUser } } = await supabase.auth.getUser();

  if (!authUser) {
    redirect("/auth/login");
  }

  // Get user profile
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/login");
  }

  // Use sample data for now (add user_id to each asset)
  const assets = sampleAssets.map(asset => ({
    ...asset,
    user_id: user.id,
  }));

  return (
    <DashboardContent
      user={user}
      initialAssets={assets}
    />
  );
}
