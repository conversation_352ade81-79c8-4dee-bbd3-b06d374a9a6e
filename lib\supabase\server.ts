import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { MediaAsset, MediaCategory, User, Group, ChatMessage, Friendship } from "../types";

export async function createClient() {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options),
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    },
  );
}

// Media asset functions
export async function getMediaAssets(
  category?: MediaCategory,
  groupId?: string,
  userId?: string
): Promise<MediaAsset[]> {
  const supabase = await createClient();

  let query = supabase
    .from('media_assets')
    .select('*')
    .order('created_at', { ascending: false });

  if (category) {
    query = query.eq('category', category);
  }

  if (groupId) {
    query = query.eq('group_id', groupId);
  }

  if (userId) {
    query = query.eq('user_id', userId);
  }

  const { data, error } = await query;

  if (error) throw error;
  return data || [];
}

export async function createMediaAsset(asset: Omit<MediaAsset, 'id' | 'created_at' | 'updated_at'>): Promise<MediaAsset> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('media_assets')
    .insert(asset)
    .select()
    .single();

  if (error) throw error;
  return data;
}

// User functions
export async function getCurrentUser(): Promise<User | null> {
  const supabase = await createClient();

  const { data: { user: authUser } } = await supabase.auth.getUser();

  if (!authUser) return null;

  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', authUser.id)
    .single();

  if (error) {
    // If user doesn't exist in public.users, return null
    return null;
  }

  // Combine auth user data with public user data
  return {
    ...data,
    email: authUser.email || '', // Get email from auth.users
  };
}

// Group functions
export async function getUserGroups(): Promise<Group[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('groups')
    .select(`
      *,
      group_members!inner(user_id)
    `)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
}
