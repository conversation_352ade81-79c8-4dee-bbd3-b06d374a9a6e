import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { MediaAsset, MediaCategory, User, Group, ChatMessage, Friendship } from "../types";

export async function createClient() {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options),
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    },
  );
}

// Media asset functions
export async function getMediaAssets(
  category?: MediaCategory,
  groupId?: string,
  userId?: string
): Promise<MediaAsset[]> {
  const supabase = await createClient();

  let query = supabase
    .from('media_assets')
    .select('*')
    .order('created_at', { ascending: false });

  if (category) {
    query = query.eq('category', category);
  }

  if (groupId) {
    query = query.eq('group_id', groupId);
  }

  if (userId) {
    query = query.eq('user_id', userId);
  }

  const { data, error } = await query;

  if (error) throw error;
  return data || [];
}

export async function createMediaAsset(asset: Omit<MediaAsset, 'id' | 'created_at' | 'updated_at'>): Promise<MediaAsset> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('media_assets')
    .insert(asset)
    .select()
    .single();

  if (error) throw error;
  return data;
}

// User functions
export async function getCurrentUser(): Promise<User | null> {
  const supabase = await createClient();

  const { data: { user: authUser } } = await supabase.auth.getUser();

  if (!authUser) return null;

  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', authUser.id)
    .single();

  if (error) {
    // If user doesn't exist in public.users, return null
    return null;
  }

  // Combine auth user data with public user data
  return {
    ...data,
    email: authUser.email || '', // Get email from auth.users
  };
}

// Media Asset functions
export async function getUserMediaAssets(userId?: string): Promise<MediaAsset[]> {
  const supabase = await createClient();

  let query = supabase
    .from('media_assets')
    .select('*')
    .order('created_at', { ascending: false });

  if (userId) {
    query = query.eq('user_id', userId);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching media assets:', error);
    return [];
  }

  return data || [];
}

export async function getPublicMediaAssets(): Promise<MediaAsset[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('media_assets')
    .select('*')
    .eq('is_public', true)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching public media assets:', error);
    return [];
  }

  return data || [];
}

export async function getMediaAssetsByCategory(category: MediaCategory, userId?: string): Promise<MediaAsset[]> {
  const supabase = await createClient();

  let query = supabase
    .from('media_assets')
    .select('*')
    .eq('category', category)
    .order('created_at', { ascending: false });

  if (userId) {
    query = query.eq('user_id', userId);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching media assets by category:', error);
    return [];
  }

  return data || [];
}

export async function getMediaAssetById(id: string): Promise<MediaAsset | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('media_assets')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching media asset:', error);
    return null;
  }

  return data;
}

export async function deleteMediaAsset(id: string): Promise<boolean> {
  const supabase = await createClient();

  const { error } = await supabase
    .from('media_assets')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting media asset:', error);
    return false;
  }

  return true;
}

export async function updateMediaAsset(id: string, updates: Partial<MediaAsset>): Promise<MediaAsset | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('media_assets')
    .update({
      ...updates,
      updated_at: new Date().toISOString(),
    })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating media asset:', error);
    return null;
  }

  return data;
}

// Group functions
export async function getUserGroups(userId: string): Promise<Group[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('groups')
    .select(`
      *,
      group_members!inner(user_id)
    `)
    .eq('group_members.user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching user groups:', error);
    return [];
  }

  return data || [];
}

export async function getGroupById(id: string): Promise<Group | null> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('groups')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching group:', error);
    return null;
  }

  return data;
}

export async function getGroupMembers(groupId: string): Promise<(GroupMember & { user: User })[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('group_members')
    .select(`
      *,
      user:users(*)
    `)
    .eq('group_id', groupId);

  if (error) {
    console.error('Error fetching group members:', error);
    return [];
  }

  return data || [];
}

// Friendship functions
export async function getUserFriends(userId: string): Promise<User[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('friendships')
    .select(`
      *,
      requester:users!friendships_requester_id_fkey(*),
      addressee:users!friendships_addressee_id_fkey(*)
    `)
    .or(`requester_id.eq.${userId},addressee_id.eq.${userId}`)
    .eq('status', 'accepted');

  if (error) {
    console.error('Error fetching user friends:', error);
    return [];
  }

  // Extract the friend (not the current user) from each friendship
  const friends = data?.map(friendship => {
    return friendship.requester_id === userId
      ? friendship.addressee
      : friendship.requester;
  }) || [];

  return friends;
}

export async function getPendingFriendRequests(userId: string): Promise<Friendship[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('friendships')
    .select(`
      *,
      requester:users!friendships_requester_id_fkey(*),
      addressee:users!friendships_addressee_id_fkey(*)
    `)
    .eq('addressee_id', userId)
    .eq('status', 'pending');

  if (error) {
    console.error('Error fetching pending friend requests:', error);
    return [];
  }

  return data || [];
}

// Chat functions
export async function getUserChatMessages(userId: string, recipientId?: string, groupId?: string): Promise<ChatMessage[]> {
  const supabase = await createClient();

  let query = supabase
    .from('chat_messages')
    .select('*')
    .order('created_at', { ascending: true });

  if (groupId) {
    query = query.eq('group_id', groupId);
  } else if (recipientId) {
    query = query
      .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
      .or(`sender_id.eq.${recipientId},recipient_id.eq.${recipientId}`);
  } else {
    query = query.or(`sender_id.eq.${userId},recipient_id.eq.${userId}`);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching chat messages:', error);
    return [];
  }

  return data || [];
}

// Group functions
export async function getUserGroups(): Promise<Group[]> {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('groups')
    .select(`
      *,
      group_members!inner(user_id)
    `)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
}
