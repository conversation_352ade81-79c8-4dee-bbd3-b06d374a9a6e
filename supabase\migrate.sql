-- ArtGrid Database Migration Script
-- Run this script in your Supabase SQL Editor to set up the database

-- First, run the schema setup
\i schema.sql

-- Then, run the policies setup
\i policies.sql

-- Finally, run the storage setup
\i storage.sql

-- Verify the setup
SELECT 'Database setup complete!' as status;

-- Check if tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'groups', 'group_members', 'media_assets', 'friendships', 'chat_messages');

-- Check if storage bucket was created
SELECT name, public 
FROM storage.buckets 
WHERE id = 'media-assets';
