-- ArtGrid Database Migration Script
-- This script migrates your existing database to the new schema with real data integration
-- Run this in your Supabase SQL Editor

BEGIN;

-- ============================================================================
-- STEP 1: Update existing tables (if they exist) or create new ones
-- ============================================================================

-- Check if users table exists and update it
DO $$
BEGIN
    -- Add missing columns to users table if they don't exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        -- Add username column if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'username' AND table_schema = 'public') THEN
            ALTER TABLE public.users ADD COLUMN username TEXT UNIQUE;
        END IF;

        -- Add display_name column if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'display_name' AND table_schema = 'public') THEN
            ALTER TABLE public.users ADD COLUMN display_name TEXT;
        END IF;

        -- Add avatar_url column if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'avatar_url' AND table_schema = 'public') THEN
            ALTER TABLE public.users ADD COLUMN avatar_url TEXT;
        END IF;

        -- Add timestamps if they don't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'created_at' AND table_schema = 'public') THEN
            ALTER TABLE public.users ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'updated_at' AND table_schema = 'public') THEN
            ALTER TABLE public.users ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        END IF;

        RAISE NOTICE 'Updated existing users table';
    ELSE
        -- Create users table if it doesn't exist
        CREATE TABLE public.users (
            id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
            username TEXT UNIQUE,
            display_name TEXT,
            avatar_url TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        RAISE NOTICE 'Created new users table';
    END IF;
END $$;

-- ============================================================================
-- STEP 2: Create or update media_assets table
-- ============================================================================

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'media_assets' AND table_schema = 'public') THEN
        -- Create custom types if they don't exist
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'media_category') THEN
            CREATE TYPE media_category AS ENUM ('2d_art', '3d_models', 'textures_materials');
        END IF;

        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'file_type') THEN
            CREATE TYPE file_type AS ENUM ('image', '3d_model', 'texture', 'material');
        END IF;

        -- Create media_assets table
        CREATE TABLE public.media_assets (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            file_url TEXT NOT NULL,
            thumbnail_url TEXT,
            file_type file_type NOT NULL,
            file_size BIGINT NOT NULL,
            mime_type TEXT NOT NULL,
            category media_category NOT NULL,
            tags TEXT[] DEFAULT '{}',
            metadata JSONB DEFAULT '{}',
            is_public BOOLEAN DEFAULT false,
            group_id UUID REFERENCES public.groups(id) ON DELETE SET NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create indexes
        CREATE INDEX idx_media_assets_user_id ON public.media_assets(user_id);
        CREATE INDEX idx_media_assets_category ON public.media_assets(category);
        CREATE INDEX idx_media_assets_group_id ON public.media_assets(group_id);
        CREATE INDEX idx_media_assets_tags ON public.media_assets USING GIN(tags);

        RAISE NOTICE 'Created media_assets table with indexes';
    ELSE
        RAISE NOTICE 'media_assets table already exists';
    END IF;
END $$;

-- ============================================================================
-- STEP 3: Create or update other tables (groups, friendships, etc.)
-- ============================================================================

DO $$
BEGIN
    -- Create enum types if they don't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'friendship_status') THEN
        CREATE TYPE friendship_status AS ENUM ('pending', 'accepted', 'blocked');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'group_role') THEN
        CREATE TYPE group_role AS ENUM ('owner', 'admin', 'member');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'message_type') THEN
        CREATE TYPE message_type AS ENUM ('text', 'media', 'system');
    END IF;

    -- Create groups table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'groups' AND table_schema = 'public') THEN
        CREATE TABLE public.groups (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            avatar_url TEXT,
            owner_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
            is_private BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        RAISE NOTICE 'Created groups table';
    END IF;

    -- Create group_members table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'group_members' AND table_schema = 'public') THEN
        CREATE TABLE public.group_members (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE NOT NULL,
            user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
            role group_role DEFAULT 'member',
            joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(group_id, user_id)
        );
        RAISE NOTICE 'Created group_members table';
    END IF;

    -- Create friendships table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'friendships' AND table_schema = 'public') THEN
        CREATE TABLE public.friendships (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            requester_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
            addressee_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
            status friendship_status DEFAULT 'pending',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(requester_id, addressee_id),
            CHECK (requester_id != addressee_id)
        );

        -- Create indexes
        CREATE INDEX idx_friendships_requester_id ON public.friendships(requester_id);
        CREATE INDEX idx_friendships_addressee_id ON public.friendships(addressee_id);

        RAISE NOTICE 'Created friendships table with indexes';
    END IF;

    -- Create chat_messages table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'chat_messages' AND table_schema = 'public') THEN
        CREATE TABLE public.chat_messages (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            sender_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
            group_id UUID REFERENCES public.groups(id) ON DELETE CASCADE,
            recipient_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
            content TEXT NOT NULL,
            message_type message_type DEFAULT 'text',
            media_url TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            CHECK (
                (group_id IS NOT NULL AND recipient_id IS NULL) OR
                (group_id IS NULL AND recipient_id IS NOT NULL)
            )
        );

        -- Create indexes
        CREATE INDEX idx_chat_messages_group_id ON public.chat_messages(group_id);
        CREATE INDEX idx_chat_messages_recipient_id ON public.chat_messages(recipient_id);
        CREATE INDEX idx_chat_messages_created_at ON public.chat_messages(created_at);

        RAISE NOTICE 'Created chat_messages table with indexes';
    END IF;
END $$;

-- ============================================================================
-- STEP 4: Create/Update Functions and Triggers
-- ============================================================================

-- Drop existing trigger and function if they exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create improved user creation function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, username, display_name, avatar_url)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', NEW.email),
    COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email),
    NEW.raw_user_meta_data->>'avatar_url'
  )
  ON CONFLICT (id) DO NOTHING; -- Prevent duplicate inserts
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ============================================================================
-- STEP 5: Enable Row Level Security
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.friendships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 6: Create/Update RLS Policies
-- ============================================================================

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view all users" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can view public media and their own media" ON public.media_assets;
DROP POLICY IF EXISTS "Users can create their own media" ON public.media_assets;
DROP POLICY IF EXISTS "Users can update their own media" ON public.media_assets;
DROP POLICY IF EXISTS "Users can delete their own media" ON public.media_assets;

-- Users policies
CREATE POLICY "Users can view all users" ON public.users
  FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Media assets policies
CREATE POLICY "Users can view public media and their own media" ON public.media_assets
  FOR SELECT USING (
    is_public OR
    user_id = auth.uid() OR
    (group_id IS NOT NULL AND group_id IN (
      SELECT group_id FROM public.group_members
      WHERE user_id = auth.uid()
    ))
  );

CREATE POLICY "Users can create their own media" ON public.media_assets
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own media" ON public.media_assets
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own media" ON public.media_assets
  FOR DELETE USING (auth.uid() = user_id);

-- Groups policies
CREATE POLICY "Users can view public groups and groups they're members of" ON public.groups
  FOR SELECT USING (
    NOT is_private OR
    id IN (
      SELECT group_id FROM public.group_members
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create groups" ON public.groups
  FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Group owners can update their groups" ON public.groups
  FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Group owners can delete their groups" ON public.groups
  FOR DELETE USING (auth.uid() = owner_id);

-- Group members policies
CREATE POLICY "Users can view group members of groups they're in" ON public.group_members
  FOR SELECT USING (
    group_id IN (
      SELECT group_id FROM public.group_members
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Group owners and admins can add members" ON public.group_members
  FOR INSERT WITH CHECK (
    group_id IN (
      SELECT group_id FROM public.group_members
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

CREATE POLICY "Group owners and admins can remove members" ON public.group_members
  FOR DELETE USING (
    group_id IN (
      SELECT group_id FROM public.group_members
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

-- Friendships policies
CREATE POLICY "Users can view their own friendships" ON public.friendships
  FOR SELECT USING (
    requester_id = auth.uid() OR
    addressee_id = auth.uid()
  );

CREATE POLICY "Users can create friendship requests" ON public.friendships
  FOR INSERT WITH CHECK (auth.uid() = requester_id);

CREATE POLICY "Users can update friendships they're involved in" ON public.friendships
  FOR UPDATE USING (
    requester_id = auth.uid() OR
    addressee_id = auth.uid()
  );

CREATE POLICY "Users can delete friendships they're involved in" ON public.friendships
  FOR DELETE USING (
    requester_id = auth.uid() OR
    addressee_id = auth.uid()
  );

-- Chat messages policies
CREATE POLICY "Users can view messages in groups they're members of" ON public.chat_messages
  FOR SELECT USING (
    (group_id IS NOT NULL AND group_id IN (
      SELECT group_id FROM public.group_members
      WHERE user_id = auth.uid()
    )) OR
    (recipient_id = auth.uid() OR sender_id = auth.uid())
  );

CREATE POLICY "Users can send messages to groups they're members of" ON public.chat_messages
  FOR INSERT WITH CHECK (
    auth.uid() = sender_id AND (
      (group_id IS NOT NULL AND group_id IN (
        SELECT group_id FROM public.group_members
        WHERE user_id = auth.uid()
      )) OR
      (recipient_id IS NOT NULL AND recipient_id IN (
        SELECT CASE
          WHEN requester_id = auth.uid() THEN addressee_id
          WHEN addressee_id = auth.uid() THEN requester_id
        END
        FROM public.friendships
        WHERE status = 'accepted' AND
        (requester_id = auth.uid() OR addressee_id = auth.uid())
      ))
    )
  );
