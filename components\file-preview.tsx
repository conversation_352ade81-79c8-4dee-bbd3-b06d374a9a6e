"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { 
  X, 
  Download, 
  Share, 
  Edit, 
  Trash2, 
  Co<PERSON>,
  ExternalLink,
  ZoomIn,
  ZoomOut,
  RotateCw
} from "lucide-react";
import { toast } from "sonner";

import { MediaAsset } from "@/lib/types";
import { fileUploader } from "@/lib/upload";
import { useMediaStore } from "@/lib/store";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface FilePreviewProps {
  asset: MediaAsset | null;
  isOpen: boolean;
  onClose: () => void;
}

export function FilePreview({ asset, isOpen, onClose }: FilePreviewProps) {
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const { removeAsset } = useMediaStore();

  if (!asset) return null;

  const handleCopyName = async () => {
    try {
      await navigator.clipboard.writeText(asset.name);
      toast.success("Asset name copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy asset name");
    }
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(asset.file_url);
      toast.success("Asset URL copied to clipboard!");
    } catch (error) {
      toast.error("Failed to copy asset URL");
    }
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = asset.file_url;
    link.download = asset.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success("Download started!");
  };

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this asset? This action cannot be undone.")) {
      return;
    }

    setIsDeleting(true);
    try {
      await fileUploader.deleteAsset(asset.id);
      removeAsset(asset.id);
      toast.success("Asset deleted successfully!");
      onClose();
    } catch (error) {
      toast.error("Failed to delete asset");
    } finally {
      setIsDeleting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const renderPreview = () => {
    if (asset.file_type === 'image' || asset.category === '2d_art') {
      return (
        <div className="relative flex items-center justify-center bg-muted/20 rounded-lg overflow-hidden">
          <motion.img
            src={asset.file_url}
            alt={asset.name}
            className="max-w-full max-h-[60vh] object-contain"
            style={{
              transform: `scale(${zoom}) rotate(${rotation}deg)`,
            }}
            transition={{ duration: 0.2 }}
          />
          
          {/* Image Controls */}
          <div className="absolute top-4 right-4 flex space-x-2">
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white"
              onClick={() => setZoom(Math.max(0.5, zoom - 0.25))}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white"
              onClick={() => setZoom(Math.min(3, zoom + 0.25))}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white"
              onClick={() => setRotation((rotation + 90) % 360)}
            >
              <RotateCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      );
    }

    if (asset.file_type === '3d_model') {
      return (
        <div className="flex items-center justify-center bg-muted/20 rounded-lg p-12">
          <div className="text-center">
            <div className="text-6xl mb-4">🗿</div>
            <p className="text-lg font-medium mb-2">3D Model Preview</p>
            <p className="text-sm text-muted-foreground mb-4">
              3D model preview coming soon
            </p>
            <Button onClick={handleDownload} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Download to View
            </Button>
          </div>
        </div>
      );
    }

    if (asset.file_type === 'texture' || asset.file_type === 'material') {
      return (
        <div className="relative flex items-center justify-center bg-muted/20 rounded-lg overflow-hidden">
          <motion.img
            src={asset.file_url}
            alt={asset.name}
            className="max-w-full max-h-[60vh] object-contain"
            style={{
              transform: `scale(${zoom})`,
              imageRendering: 'pixelated', // Better for textures
            }}
            transition={{ duration: 0.2 }}
          />
          
          {/* Texture Controls */}
          <div className="absolute top-4 right-4 flex space-x-2">
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white"
              onClick={() => setZoom(Math.max(0.5, zoom - 0.25))}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 bg-black/50 hover:bg-black/70 text-white"
              onClick={() => setZoom(Math.min(5, zoom + 0.25))}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center bg-muted/20 rounded-lg p-12">
        <div className="text-center">
          <div className="text-6xl mb-4">📄</div>
          <p className="text-lg font-medium mb-2">File Preview</p>
          <p className="text-sm text-muted-foreground mb-4">
            Preview not available for this file type
          </p>
          <Button onClick={handleDownload} variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Download File
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl">{asset.name}</DialogTitle>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="icon" onClick={handleCopyName}>
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" onClick={handleDownload}>
                <Download className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="icon" 
                onClick={handleDelete}
                disabled={isDeleting}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Preview Area */}
          <div className="lg:col-span-2">
            {renderPreview()}
          </div>

          {/* Asset Info */}
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Details</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Type:</span>
                  <Badge variant="outline">{asset.category.replace('_', ' ')}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Size:</span>
                  <span>{formatFileSize(asset.file_size)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Format:</span>
                  <span>{asset.mime_type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Created:</span>
                  <span>{new Date(asset.created_at).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            {asset.description && (
              <div>
                <h3 className="font-medium mb-2">Description</h3>
                <p className="text-sm text-muted-foreground">{asset.description}</p>
              </div>
            )}

            {asset.tags.length > 0 && (
              <div>
                <h3 className="font-medium mb-2">Tags</h3>
                <div className="flex flex-wrap gap-1">
                  {asset.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <Separator />

            <div className="space-y-2">
              <Button 
                variant="outline" 
                className="w-full justify-start" 
                onClick={handleCopyUrl}
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Copy URL
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start" 
                onClick={handleDownload}
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
