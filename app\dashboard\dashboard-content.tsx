"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Grid3X3, List, SlidersHorizontal, Upload } from "lucide-react";
import { toast } from "sonner";

import { MediaAsset, MediaCategory, User } from "@/lib/types";
import { useMediaStore, useUserStore, useUIStore } from "@/lib/store";

import { DashboardLayout } from "@/components/dashboard-layout";
import { SearchBar } from "@/components/search-bar";
import { CategoryFilter } from "@/components/category-filter";
import { MediaCard } from "@/components/media-card";
import { MediaGridSkeleton, EmptyState } from "@/components/loading-states";
import { UploadModal } from "@/components/upload-modal";
import { FilePreview } from "@/components/file-preview";
import { BulkOperations } from "@/components/bulk-operations";
import { AdvancedFilters } from "@/components/advanced-filters";
import { MediaEditor } from "@/components/media-editor";
import { StatsDashboard } from "@/components/stats-dashboard";
import { KeyboardShortcuts } from "@/components/keyboard-shortcuts";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface DashboardContentProps {
  user: User;
  initialAssets: MediaAsset[];
}

export function DashboardContent({ user, initialAssets }: DashboardContentProps) {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [previewAsset, setPreviewAsset] = useState<MediaAsset | null>(null);
  const [editAsset, setEditAsset] = useState<MediaAsset | null>(null);
  
  const {
    assets,
    filteredAssets,
    currentFilter,
    isLoading,
    selectedAssets,
    setAssets,
    setFilter,
    toggleAssetSelection,
    clearSelection,
  } = useMediaStore();

  const { setCurrentUser } = useUserStore();
  const { setUploadModalOpen } = useUIStore();

  // Initialize stores
  useEffect(() => {
    setCurrentUser(user);
    setAssets(initialAssets);
  }, [user, initialAssets, setCurrentUser, setAssets]);

  // Update filter when search or tags change
  useEffect(() => {
    setFilter({
      ...currentFilter,
      search: searchQuery || undefined,
      tags: selectedTags.length > 0 ? selectedTags : undefined,
    });
  }, [searchQuery, selectedTags, setFilter]);

  // Get all available tags from assets
  const availableTags = Array.from(
    new Set(assets.flatMap(asset => asset.tags))
  ).sort();

  // Get category counts
  const categoryCounts = assets.reduce((counts, asset) => {
    counts[asset.category] = (counts[asset.category] || 0) + 1;
    return counts;
  }, {} as Record<MediaCategory, number>);

  const handleCategoryChange = (category?: MediaCategory) => {
    setFilter({
      ...currentFilter,
      category,
    });
  };

  const handleAssetPreview = (asset: MediaAsset) => {
    setPreviewAsset(asset);
  };

  const handleAssetEdit = (asset: MediaAsset) => {
    setEditAsset(asset);
  };

  const handleSelectAll = () => {
    const allAssetIds = filteredAssets.map(asset => asset.id);
    allAssetIds.forEach(id => {
      if (!selectedAssets.includes(id)) {
        toggleAssetSelection(id);
      }
    });
  };

  const handleBulkDelete = () => {
    if (selectedAssets.length === 0) return;
    // This will be handled by the BulkOperations component
  };

  const hasAssets = filteredAssets.length > 0;
  const hasAnyAssets = assets.length > 0;

  return (
    <DashboardLayout user={user}>
      <KeyboardShortcuts
        onSelectAll={handleSelectAll}
        onClearSelection={clearSelection}
        onDelete={handleBulkDelete}
        onUpload={() => setUploadModalOpen(true)}
      />
      <UploadModal />
      <FilePreview
        asset={previewAsset}
        isOpen={!!previewAsset}
        onClose={() => setPreviewAsset(null)}
      />
      <MediaEditor
        asset={editAsset}
        isOpen={!!editAsset}
        onClose={() => setEditAsset(null)}
      />
      <div className="flex h-screen">
        {/* Sidebar Filters */}
        <div className="hidden lg:block w-80 border-r border-border/50 bg-card/30 backdrop-blur-sm">
          <div className="p-6 space-y-6">
            <div>
              <h2 className="text-lg font-semibold mb-4">Filters</h2>
              <CategoryFilter
                selectedCategory={currentFilter.category}
                onCategoryChange={handleCategoryChange}
                counts={categoryCounts}
              />
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Header */}
          <div className="border-b border-border/50 bg-background/80 backdrop-blur-md sticky top-0 z-30">
            <div className="p-6 space-y-4">
              {/* Title and Actions */}
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold">Media Library</h1>
                  <p className="text-muted-foreground">
                    {hasAnyAssets 
                      ? `${filteredAssets.length} of ${assets.length} assets`
                      : "No assets yet"
                    }
                  </p>
                </div>
                
                <div className="flex items-center space-x-2">
                  {/* Advanced Filters */}
                  <AdvancedFilters
                    currentFilter={currentFilter}
                    onFilterChange={setFilter}
                    availableTags={availableTags}
                  />

                  {/* View Mode Toggle */}
                  <div className="flex items-center border border-border rounded-lg p-1">
                    <Button
                      variant={viewMode === "grid" ? "secondary" : "ghost"}
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setViewMode("grid")}
                    >
                      <Grid3X3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === "list" ? "secondary" : "ghost"}
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setViewMode("list")}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>

                  <Button onClick={() => setUploadModalOpen(true)}>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload
                  </Button>
                </div>
              </div>

              {/* Search Bar */}
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                onTagsChange={setSelectedTags}
                selectedTags={selectedTags}
                availableTags={availableTags}
                placeholder="Search your media library..."
              />
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 p-6 space-y-6">
            {/* Stats Dashboard */}
            {hasAnyAssets && (
              <StatsDashboard assets={assets} />
            )}

            {isLoading ? (
              <MediaGridSkeleton />
            ) : hasAssets ? (
              <motion.div
                layout
                className={cn(
                  viewMode === "grid" 
                    ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6"
                    : "space-y-4"
                )}
              >
                <AnimatePresence mode="popLayout">
                  {filteredAssets.map((asset) => (
                    <MediaCard
                      key={asset.id}
                      asset={asset}
                      isSelected={selectedAssets.includes(asset.id)}
                      onSelect={toggleAssetSelection}
                      onPreview={handleAssetPreview}
                      onEdit={handleAssetEdit}
                    />
                  ))}
                </AnimatePresence>
              </motion.div>
            ) : hasAnyAssets ? (
              <EmptyState
                icon={<SlidersHorizontal className="h-12 w-12" />}
                title="No assets match your filters"
                description="Try adjusting your search terms or filters to find what you're looking for."
                action={
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchQuery("");
                      setSelectedTags([]);
                      setFilter({});
                    }}
                  >
                    Clear Filters
                  </Button>
                }
              />
            ) : (
              <EmptyState
                icon={<Upload className="h-12 w-12" />}
                title="No media assets yet"
                description="Start building your library by uploading your first digital asset."
                action={
                  <Button onClick={() => setUploadModalOpen(true)}>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Your First Asset
                  </Button>
                }
              />
            )}
          </div>
        </div>
      </div>

      {/* Bulk Operations */}
      <BulkOperations
        selectedAssets={selectedAssets}
        assets={assets}
        onClearSelection={clearSelection}
      />
    </DashboardLayout>
  );
}
