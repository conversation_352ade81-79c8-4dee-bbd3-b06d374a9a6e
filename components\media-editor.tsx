"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Edit, Save, X, Tag, FileText, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";

import { MediaAsset, MediaCategory } from "@/lib/types";
import { createClient } from "@/lib/supabase/client";
import { useMediaStore } from "@/lib/store";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";

interface MediaEditorProps {
  asset: MediaAsset | null;
  isOpen: boolean;
  onClose: () => void;
}

export function MediaEditor({ asset, isOpen, onClose }: MediaEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: asset?.name || "",
    description: asset?.description || "",
    category: asset?.category || "2d_art" as MediaCategory,
    tags: asset?.tags || [],
    is_public: asset?.is_public || false,
  });
  const [tagInput, setTagInput] = useState("");

  const { updateAsset } = useMediaStore();
  const supabase = createClient();

  if (!asset) return null;

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const { data, error } = await supabase
        .from('media_assets')
        .update({
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          category: formData.category,
          tags: formData.tags,
          is_public: formData.is_public,
        })
        .eq('id', asset.id)
        .select()
        .single();

      if (error) throw error;

      updateAsset(asset.id, data);
      setIsEditing(false);
      toast.success("Asset updated successfully!");
    } catch (error) {
      toast.error("Failed to update asset");
      console.error('Asset update error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: asset.name,
      description: asset.description || "",
      category: asset.category,
      tags: asset.tags,
      is_public: asset.is_public,
    });
    setIsEditing(false);
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center space-x-2">
              <Edit className="h-5 w-5" />
              <span>Edit Asset</span>
            </DialogTitle>
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            ) : (
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={isSaving || !formData.name.trim()}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? "Saving..." : "Save"}
                </Button>
              </div>
            )}
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Preview */}
          <div className="space-y-4">
            <h3 className="font-medium">Preview</h3>
            <div className="aspect-square bg-muted rounded-lg overflow-hidden">
              <img
                src={asset.thumbnail_url || asset.file_url}
                alt={asset.name}
                className="w-full h-full object-cover"
              />
            </div>
            
            {/* File Info */}
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">File Size:</span>
                <span>{formatFileSize(asset.file_size)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Format:</span>
                <span>{asset.mime_type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Uploaded:</span>
                <span>{new Date(asset.created_at).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Modified:</span>
                <span>{new Date(asset.updated_at).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          {/* Edit Form */}
          <div className="space-y-4">
            <h3 className="font-medium">Asset Details</h3>
            
            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="asset-name">Name *</Label>
              {isEditing ? (
                <Input
                  id="asset-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Asset name"
                />
              ) : (
                <div className="p-2 bg-muted rounded-md">
                  {asset.name}
                </div>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="asset-description">Description</Label>
              {isEditing ? (
                <Textarea
                  id="asset-description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Asset description"
                  rows={3}
                />
              ) : (
                <div className="p-2 bg-muted rounded-md min-h-[80px]">
                  {asset.description || "No description"}
                </div>
              )}
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label>Category</Label>
              {isEditing ? (
                <Select
                  value={formData.category}
                  onValueChange={(value: MediaCategory) => 
                    setFormData(prev => ({ ...prev, category: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2d_art">2D Art</SelectItem>
                    <SelectItem value="3d_models">3D Models</SelectItem>
                    <SelectItem value="textures_materials">Textures & Materials</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div className="p-2 bg-muted rounded-md">
                  {asset.category.replace('_', ' ')}
                </div>
              )}
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <Label>Tags</Label>
              {isEditing ? (
                <div className="space-y-2">
                  <div className="flex space-x-2">
                    <Input
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      placeholder="Add tag"
                      onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                    />
                    <Button type="button" variant="outline" onClick={handleAddTag}>
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {formData.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 ml-1 hover:bg-destructive hover:text-destructive-foreground"
                          onClick={() => handleRemoveTag(tag)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="flex flex-wrap gap-1">
                  {asset.tags.length > 0 ? (
                    asset.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-muted-foreground text-sm">No tags</span>
                  )}
                </div>
              )}
            </div>

            <Separator />

            {/* Visibility */}
            <div className="space-y-2">
              <Label className="flex items-center space-x-2">
                {formData.is_public ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                <span>Visibility</span>
              </Label>
              {isEditing ? (
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={formData.is_public}
                    onCheckedChange={(checked) => 
                      setFormData(prev => ({ ...prev, is_public: checked }))
                    }
                  />
                  <span className="text-sm">
                    {formData.is_public ? "Public" : "Private"}
                  </span>
                </div>
              ) : (
                <div className="p-2 bg-muted rounded-md">
                  {asset.is_public ? "Public" : "Private"}
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
