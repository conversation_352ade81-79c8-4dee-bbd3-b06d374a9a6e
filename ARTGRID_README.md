# ArtGrid - Premium Digital Asset Management Platform

A sophisticated, modern web application for managing and organizing digital art assets, 3D models, and textures. Built with Next.js, Supabase, and premium UI components for a professional creative workflow.

## ✨ Features

### 🎨 **Media Management**
- **Multi-format Support**: 2D artwork, 3D models, textures, and materials
- **Smart Organization**: Category-based filtering with advanced search
- **Bulk Operations**: Select, tag, download, and manage multiple assets
- **Real-time Preview**: Instant preview with zoom, rotation, and metadata display
- **Intelligent Thumbnails**: Automatic thumbnail generation for images

### 🔐 **Authentication & Security**
- **Secure Authentication**: Email/password with <PERSON>pa<PERSON> Auth
- **Protected Routes**: Dashboard access restricted to authenticated users
- **Row-Level Security**: Database-level security policies
- **Session Management**: Persistent sessions with automatic refresh

### 👥 **Social Features**
- **Friend System**: Send/accept friend requests and manage connections
- **Group Collaboration**: Create private/public groups for team projects
- **Real-time Chat**: Direct messages and group chat with live updates
- **User Profiles**: Customizable profiles with avatar uploads

### 🚀 **Advanced Functionality**
- **File Upload System**: Drag-and-drop with progress tracking
- **Advanced Filtering**: Filter by size, date, tags, visibility, and more
- **Keyboard Shortcuts**: Power-user shortcuts for efficient workflow
- **Real-time Notifications**: Live updates for uploads, messages, and requests
- **Analytics Dashboard**: Usage statistics and asset insights

### 🎯 **Premium UX/UI**
- **Apple-inspired Design**: Clean, minimalist interface with premium feel
- **Smooth Animations**: Framer Motion powered micro-interactions
- **Responsive Design**: Perfect on desktop, tablet, and mobile
- **Dark/Light Themes**: System-aware theme switching
- **Glass Morphism**: Modern glassmorphic design elements

## 🛠 Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Realtime)
- **Styling**: Tailwind CSS, shadcn/ui components
- **Animations**: Framer Motion
- **State Management**: Zustand
- **File Handling**: React Dropzone
- **Notifications**: Sonner (toast notifications)

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd artgrid
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up Supabase**
   - Create a new project at [supabase.com](https://supabase.com)
   - Copy your project URL and anon key
   - Run the database migrations (see Database Setup below)

4. **Environment Configuration**
```bash
cp .env.local.example .env.local
```

Add your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

5. **Database Setup**
Run the SQL files in your Supabase SQL editor:
```sql
-- Run in order:
-- 1. supabase/schema.sql
-- 2. supabase/policies.sql  
-- 3. supabase/storage.sql
```

6. **Start Development Server**
```bash
npm run dev
```

7. **Open Application**
Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
artgrid/
├── app/                    # Next.js App Router
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Main dashboard
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── ui/               # shadcn/ui base components
│   ├── media-card.tsx    # Asset display component
│   ├── upload-modal.tsx  # File upload interface
│   └── ...               # Feature components
├── lib/                  # Utilities and configuration
│   ├── supabase/         # Supabase client setup
│   ├── types.ts          # TypeScript definitions
│   ├── store.ts          # Zustand state management
│   └── upload.ts         # File upload utilities
├── supabase/             # Database schema and policies
│   ├── schema.sql        # Database tables
│   ├── policies.sql      # Row Level Security
│   └── storage.sql       # Storage bucket setup
└── public/               # Static assets
```

## 🎮 Usage

### Basic Workflow
1. **Sign up/Login** to access the dashboard
2. **Upload Assets** using drag-and-drop or the upload button
3. **Organize** with categories, tags, and descriptions
4. **Preview** assets with the built-in viewer
5. **Share** with friends or collaborate in groups
6. **Chat** with team members in real-time

### Keyboard Shortcuts
- `Ctrl+A` - Select all assets
- `Ctrl+U` - Open upload modal
- `Delete` - Delete selected assets
- `Esc` - Clear selection
- `/` - Focus search bar
- `?` - Show shortcuts help

## 🔧 Configuration

### Supabase Setup
The application requires several Supabase features:
- **Database**: PostgreSQL with custom tables
- **Auth**: Email/password authentication
- **Storage**: File storage for media assets
- **Realtime**: Live updates for chat and notifications

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Add environment variables
4. Deploy automatically

### Other Platforms
The app can be deployed to any platform supporting Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org) for the amazing framework
- [Supabase](https://supabase.com) for the backend infrastructure
- [shadcn/ui](https://ui.shadcn.com) for beautiful components
- [Framer Motion](https://framer.com/motion) for smooth animations
- [Tailwind CSS](https://tailwindcss.com) for utility-first styling

## 📞 Support

For support, email <EMAIL> or join our Discord community.

---

Built with ❤️ for the creative community
